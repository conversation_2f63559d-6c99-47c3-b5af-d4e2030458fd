<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة روافد B2B</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .rtl { direction: rtl; }
    </style>
</head>
<body class="bg-gray-50 rtl">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // API Service
        const API_BASE_URL = 'http://localhost:2340/api';

        const apiService = {
            async request(endpoint, options = {}) {
                const url = `${API_BASE_URL}${endpoint}`;
                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options,
                };

                try {
                    const response = await fetch(url, config);
                    const data = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(data.error || 'API request failed');
                    }
                    
                    return data;
                } catch (error) {
                    console.error(`API Error (${endpoint}):`, error);
                    throw error;
                }
            },

            async login(email, password) {
                return await this.request('/v1/auth/login', {
                    method: 'POST',
                    body: JSON.stringify({ email, password }),
                });
            },

            async register(userData) {
                return await this.request('/v1/auth/register', {
                    method: 'POST',
                    body: JSON.stringify(userData),
                });
            },

            async getProducts() {
                return await this.request('/v1/products');
            },

            async healthCheck() {
                return await this.request('/health');
            }
        };

        // Login Component
        function LoginForm({ onLogin, onSwitchToRegister }) {
            const [email, setEmail] = useState('');
            const [password, setPassword] = useState('');
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState('');

            const handleSubmit = async (e) => {
                e.preventDefault();
                setLoading(true);
                setError('');

                try {
                    const response = await apiService.login(email, password);
                    localStorage.setItem('authToken', response.access_token);
                    localStorage.setItem('user', JSON.stringify(response.user));
                    onLogin(response.user);
                } catch (error) {
                    setError(error.message);
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                    <div className="max-w-md w-full space-y-8">
                        <div>
                            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                                تسجيل الدخول إلى منصة روافد
                            </h2>
                        </div>
                        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
                            {error && (
                                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                                    {error}
                                </div>
                            )}
                            <div className="rounded-md shadow-sm -space-y-px">
                                <div>
                                    <input
                                        type="email"
                                        required
                                        className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                                        placeholder="البريد الإلكتروني"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                    />
                                </div>
                                <div>
                                    <input
                                        type="password"
                                        required
                                        className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                                        placeholder="كلمة المرور"
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                    />
                                </div>
                            </div>

                            <div>
                                <button
                                    type="submit"
                                    disabled={loading}
                                    className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                >
                                    {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
                                </button>
                            </div>

                            <div className="text-center">
                                <button
                                    type="button"
                                    onClick={onSwitchToRegister}
                                    className="text-indigo-600 hover:text-indigo-500"
                                >
                                    ليس لديك حساب؟ سجل الآن
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            );
        }

        // Register Component
        function RegisterForm({ onRegister, onSwitchToLogin }) {
            const [formData, setFormData] = useState({
                email: '',
                phone: '',
                password: '',
                user_type: 'buyer',
                first_name: '',
                last_name: '',
                company: {
                    business_name: '',
                    business_name_ar: '',
                    industry_sector: 'retail'
                }
            });
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState('');

            const handleSubmit = async (e) => {
                e.preventDefault();
                setLoading(true);
                setError('');

                try {
                    const response = await apiService.register(formData);
                    localStorage.setItem('authToken', response.access_token);
                    localStorage.setItem('user', JSON.stringify(response.user));
                    onRegister(response.user);
                } catch (error) {
                    setError(error.message);
                } finally {
                    setLoading(false);
                }
            };

            const handleChange = (e) => {
                const { name, value } = e.target;
                if (name.startsWith('company.')) {
                    const companyField = name.split('.')[1];
                    setFormData(prev => ({
                        ...prev,
                        company: {
                            ...prev.company,
                            [companyField]: value
                        }
                    }));
                } else {
                    setFormData(prev => ({
                        ...prev,
                        [name]: value
                    }));
                }
            };

            return (
                <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                    <div className="max-w-md w-full space-y-8">
                        <div>
                            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                                إنشاء حساب جديد
                            </h2>
                        </div>
                        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
                            {error && (
                                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                                    {error}
                                </div>
                            )}
                            
                            <div className="space-y-4">
                                <input
                                    type="text"
                                    name="first_name"
                                    required
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="الاسم الأول"
                                    value={formData.first_name}
                                    onChange={handleChange}
                                />
                                
                                <input
                                    type="text"
                                    name="last_name"
                                    required
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="اسم العائلة"
                                    value={formData.last_name}
                                    onChange={handleChange}
                                />
                                
                                <input
                                    type="email"
                                    name="email"
                                    required
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="البريد الإلكتروني"
                                    value={formData.email}
                                    onChange={handleChange}
                                />
                                
                                <input
                                    type="tel"
                                    name="phone"
                                    required
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="رقم الهاتف"
                                    value={formData.phone}
                                    onChange={handleChange}
                                />
                                
                                <input
                                    type="password"
                                    name="password"
                                    required
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="كلمة المرور"
                                    value={formData.password}
                                    onChange={handleChange}
                                />
                                
                                <select
                                    name="user_type"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    value={formData.user_type}
                                    onChange={handleChange}
                                >
                                    <option value="buyer">مشتري</option>
                                    <option value="supplier">مورد</option>
                                </select>
                                
                                <input
                                    type="text"
                                    name="company.business_name"
                                    required
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="اسم الشركة (بالإنجليزية)"
                                    value={formData.company.business_name}
                                    onChange={handleChange}
                                />
                                
                                <input
                                    type="text"
                                    name="company.business_name_ar"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="اسم الشركة (بالعربية)"
                                    value={formData.company.business_name_ar}
                                    onChange={handleChange}
                                />
                            </div>

                            <div>
                                <button
                                    type="submit"
                                    disabled={loading}
                                    className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                >
                                    {loading ? 'جاري إنشاء الحساب...' : 'إنشاء حساب'}
                                </button>
                            </div>

                            <div className="text-center">
                                <button
                                    type="button"
                                    onClick={onSwitchToLogin}
                                    className="text-indigo-600 hover:text-indigo-500"
                                >
                                    لديك حساب بالفعل؟ سجل دخولك
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            );
        }

        // Dashboard Component
        function Dashboard({ user, onLogout }) {
            const [products, setProducts] = useState([]);
            const [loading, setLoading] = useState(true);

            useEffect(() => {
                loadProducts();
            }, []);

            const loadProducts = async () => {
                try {
                    const response = await apiService.getProducts();
                    setProducts(response.products || []);
                } catch (error) {
                    console.error('Error loading products:', error);
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div className="min-h-screen bg-gray-50">
                    {/* Header */}
                    <header className="bg-white shadow">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="flex justify-between items-center py-6">
                                <div className="flex items-center">
                                    <h1 className="text-3xl font-bold text-gray-900">منصة روافد B2B</h1>
                                </div>
                                <div className="flex items-center space-x-4">
                                    <a href="products.html" className="text-indigo-600 hover:text-indigo-500 px-3 py-2">المنتجات</a>
                                    <a href="orders.html" className="text-indigo-600 hover:text-indigo-500 px-3 py-2">الطلبات</a>
                                    <a href="suppliers.html" className="text-indigo-600 hover:text-indigo-500 px-3 py-2">الموردين</a>
                                    <span className="text-gray-700">مرحباً، {user.first_name}</span>
                                    <button
                                        onClick={onLogout}
                                        className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                                    >
                                        تسجيل الخروج
                                    </button>
                                </div>
                            </div>
                        </div>
                    </header>

                    {/* Main Content */}
                    <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                        <div className="px-4 py-6 sm:px-0">
                            <div className="border-4 border-dashed border-gray-200 rounded-lg p-6">
                                <h2 className="text-2xl font-bold text-gray-900 mb-6">لوحة التحكم</h2>
                                
                                {/* User Info */}
                                <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
                                    <div className="px-4 py-5 sm:p-6">
                                        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">معلومات المستخدم</h3>
                                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                            <div>
                                                <p className="text-sm font-medium text-gray-500">الاسم</p>
                                                <p className="mt-1 text-sm text-gray-900">{user.first_name} {user.last_name}</p>
                                            </div>
                                            <div>
                                                <p className="text-sm font-medium text-gray-500">البريد الإلكتروني</p>
                                                <p className="mt-1 text-sm text-gray-900">{user.email}</p>
                                            </div>
                                            <div>
                                                <p className="text-sm font-medium text-gray-500">نوع المستخدم</p>
                                                <p className="mt-1 text-sm text-gray-900">{user.user_type === 'buyer' ? 'مشتري' : 'مورد'}</p>
                                            </div>
                                            <div>
                                                <p className="text-sm font-medium text-gray-500">الحالة</p>
                                                <p className="mt-1 text-sm text-green-600">{user.status === 'active' ? 'نشط' : 'غير نشط'}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Products */}
                                <div className="bg-white overflow-hidden shadow rounded-lg">
                                    <div className="px-4 py-5 sm:p-6">
                                        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">المنتجات المتاحة</h3>
                                        {loading ? (
                                            <p className="text-gray-500">جاري تحميل المنتجات...</p>
                                        ) : products.length > 0 ? (
                                            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                                                {products.map((product) => (
                                                    <div key={product.id} className="border border-gray-200 rounded-lg p-4">
                                                        <h4 className="font-medium text-gray-900">{product.name}</h4>
                                                        <p className="text-sm text-gray-500 mt-1">{product.name_ar}</p>
                                                        <p className="text-sm text-gray-600 mt-2">{product.description}</p>
                                                        <div className="mt-3 flex justify-between items-center">
                                                            <span className="text-lg font-bold text-green-600">{product.unit_price} ريال</span>
                                                            <span className="text-sm text-gray-500">/{product.unit_of_measure}</span>
                                                        </div>
                                                        <p className="text-xs text-gray-400 mt-1">الحد الأدنى: {product.minimum_order_quantity}</p>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <p className="text-gray-500">لا توجد منتجات متاحة حالياً</p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </main>
                </div>
            );
        }

        // Main App Component
        function App() {
            const [user, setUser] = useState(null);
            const [currentView, setCurrentView] = useState('login');
            const [backendStatus, setBackendStatus] = useState('checking');

            useEffect(() => {
                // Check if user is already logged in
                const savedUser = localStorage.getItem('user');
                if (savedUser) {
                    setUser(JSON.parse(savedUser));
                }

                // Check backend status
                checkBackendStatus();
            }, []);

            const checkBackendStatus = async () => {
                try {
                    await apiService.healthCheck();
                    setBackendStatus('connected');
                } catch (error) {
                    setBackendStatus('disconnected');
                }
            };

            const handleLogin = (userData) => {
                setUser(userData);
                setCurrentView('dashboard');
            };

            const handleRegister = (userData) => {
                setUser(userData);
                setCurrentView('dashboard');
            };

            const handleLogout = () => {
                localStorage.removeItem('authToken');
                localStorage.removeItem('user');
                setUser(null);
                setCurrentView('login');
            };

            // Show backend status
            if (backendStatus === 'checking') {
                return (
                    <div className="min-h-screen flex items-center justify-center bg-gray-50">
                        <div className="text-center">
                            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto"></div>
                            <p className="mt-4 text-lg text-gray-600">جاري الاتصال بالخادم...</p>
                        </div>
                    </div>
                );
            }

            if (backendStatus === 'disconnected') {
                return (
                    <div className="min-h-screen flex items-center justify-center bg-gray-50">
                        <div className="text-center">
                            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded max-w-md mx-auto">
                                <h2 className="text-xl font-bold mb-2">خطأ في الاتصال</h2>
                                <p>لا يمكن الاتصال بالخادم. تأكد من تشغيل الخادم على المنفذ 2340</p>
                                <button 
                                    onClick={checkBackendStatus}
                                    className="mt-4 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded"
                                >
                                    إعادة المحاولة
                                </button>
                            </div>
                        </div>
                    </div>
                );
            }

            // Show appropriate view
            if (user) {
                return <Dashboard user={user} onLogout={handleLogout} />;
            }

            if (currentView === 'register') {
                return (
                    <RegisterForm 
                        onRegister={handleRegister} 
                        onSwitchToLogin={() => setCurrentView('login')} 
                    />
                );
            }

            return (
                <LoginForm 
                    onLogin={handleLogin} 
                    onSwitchToRegister={() => setCurrentView('register')} 
                />
            );
        }

        // Render the app
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
