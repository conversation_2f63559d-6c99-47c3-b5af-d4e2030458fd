import os
import sys
# DON'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, jsonify, request
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>, create_access_token, jwt_required, get_jwt_identity
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import re

app = Flask(__name__)

# Configuration
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///rawafed_simple.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_SECRET_KEY'] = 'your-super-secret-jwt-key-change-this-in-production'
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

# Initialize extensions
db = SQLAlchemy(app)
jwt = JWTManager(app)
CORS(app, origins=['http://localhost:3000', 'http://localhost:5173'],
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
     allow_headers=['Content-Type', 'Authorization'])

# Simple User model
class User(db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(255), unique=True, nullable=False)
    phone = db.Column(db.String(20), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    user_type = db.Column(db.String(20), nullable=False)  # supplier, buyer, admin
    first_name = db.Column(db.String(100))
    last_name = db.Column(db.String(100))
    status = db.Column(db.String(20), default='active')
    email_verified = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        return {
            'id': self.id,
            'email': self.email,
            'phone': self.phone,
            'user_type': self.user_type,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'status': self.status,
            'email_verified': self.email_verified,
            'created_at': self.created_at.isoformat()
        }

# Simple Company model
class Company(db.Model):
    __tablename__ = 'companies'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    business_name = db.Column(db.String(255), nullable=False)
    business_name_ar = db.Column(db.String(255))
    business_type = db.Column(db.String(50), nullable=False)
    commercial_registration = db.Column(db.String(100))
    industry_sector = db.Column(db.String(100))
    verification_status = db.Column(db.String(20), default='pending')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationship
    user = db.relationship('User', backref='company')
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'business_name': self.business_name,
            'business_name_ar': self.business_name_ar,
            'business_type': self.business_type,
            'commercial_registration': self.commercial_registration,
            'industry_sector': self.industry_sector,
            'verification_status': self.verification_status,
            'created_at': self.created_at.isoformat()
        }

# Simple Product model
class Product(db.Model):
    __tablename__ = 'products'
    
    id = db.Column(db.Integer, primary_key=True)
    supplier_id = db.Column(db.Integer, db.ForeignKey('companies.id'), nullable=False)
    sku = db.Column(db.String(100), unique=True, nullable=False)
    name = db.Column(db.String(255), nullable=False)
    name_ar = db.Column(db.String(255))
    description = db.Column(db.Text)
    unit_of_measure = db.Column(db.String(50), nullable=False)
    unit_price = db.Column(db.Numeric(12, 4), default=0)
    minimum_order_quantity = db.Column(db.Integer, default=1)
    status = db.Column(db.String(20), default='active')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationship
    supplier = db.relationship('Company', backref='products')
    
    def to_dict(self):
        return {
            'id': self.id,
            'supplier_id': self.supplier_id,
            'sku': self.sku,
            'name': self.name,
            'name_ar': self.name_ar,
            'description': self.description,
            'unit_of_measure': self.unit_of_measure,
            'unit_price': float(self.unit_price) if self.unit_price else 0,
            'minimum_order_quantity': self.minimum_order_quantity,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'supplier': {
                'id': self.supplier.id,
                'business_name': self.supplier.business_name,
                'business_name_ar': self.supplier.business_name_ar
            } if self.supplier else None
        }

# Helper functions
def validate_email(email):
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

# Routes
@app.route('/api/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'message': 'Rawafed B2B API is running',
        'version': '1.0.0'
    })

@app.route('/api/v1/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        
        if not data.get('email') or not data.get('password'):
            return jsonify({'error': 'Email and password are required'}), 400
        
        email = data['email'].lower().strip()
        password = data['password']
        
        user = User.query.filter_by(email=email).first()
        
        if not user or not user.check_password(password):
            return jsonify({'error': 'Invalid email or password'}), 401
        
        if user.status != 'active':
            return jsonify({'error': 'Account is not active'}), 403
        
        # Create JWT token
        company_id = None
        if hasattr(user, 'company') and user.company:
            if isinstance(user.company, list) and len(user.company) > 0:
                company_id = user.company[0].id
            elif hasattr(user.company, 'id'):
                company_id = user.company.id
        
        access_token = create_access_token(
            identity=user.id,
            additional_claims={
                'user_type': user.user_type,
                'company_id': company_id,
                'email': user.email
            }
        )
        
        # Get company data
        company_data = None
        if hasattr(user, 'company') and user.company:
            if isinstance(user.company, list) and len(user.company) > 0:
                company_data = user.company[0].to_dict()
            elif hasattr(user.company, 'to_dict'):
                company_data = user.company.to_dict()
        
        return jsonify({
            'message': 'Login successful',
            'access_token': access_token,
            'token_type': 'Bearer',
            'expires_in': app.config['JWT_ACCESS_TOKEN_EXPIRES'].total_seconds(),
            'user': user.to_dict(),
            'company': company_data
        }), 200
        
    except Exception as e:
        print(f"Login error: {str(e)}")  # Add logging
        return jsonify({'error': f'Login failed: {str(e)}'}), 500

@app.route('/api/v1/auth/register', methods=['POST'])
def register():
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['email', 'phone', 'password', 'user_type']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'{field} is required'}), 400
        
        email = data['email'].lower().strip()
        phone = data['phone'].strip()
        password = data['password']
        user_type = data['user_type']
        
        if not validate_email(email):
            return jsonify({'error': 'Invalid email format'}), 400
        
        if user_type not in ['supplier', 'buyer']:
            return jsonify({'error': 'Invalid user type'}), 400
        
        # Check if user already exists
        existing_user = User.query.filter(
            (User.email == email) | (User.phone == phone)
        ).first()
        
        if existing_user:
            return jsonify({'error': 'User with this email or phone already exists'}), 409
        
        # Create new user
        new_user = User(
            email=email,
            phone=phone,
            user_type=user_type,
            first_name=data.get('first_name'),
            last_name=data.get('last_name')
        )
        new_user.set_password(password)
        new_user.status = 'active'
        new_user.email_verified = True
        
        db.session.add(new_user)
        db.session.flush()
        
        # Create company profile
        company_data = data.get('company', {})
        if company_data.get('business_name'):
            new_company = Company(
                user_id=new_user.id,
                business_name=company_data['business_name'],
                business_name_ar=company_data.get('business_name_ar'),
                business_type=user_type,
                commercial_registration=company_data.get('commercial_registration'),
                industry_sector=company_data.get('industry_sector'),
                verification_status='verified'
            )
            db.session.add(new_company)
        
        db.session.commit()
        
        # Create JWT token
        access_token = create_access_token(
            identity=new_user.id,
            additional_claims={
                'user_type': new_user.user_type,
                'company_id': new_user.company.id if new_user.company else None,
                'email': new_user.email
            }
        )
        
        return jsonify({
            'message': 'User registered successfully',
            'access_token': access_token,
            'token_type': 'Bearer',
            'expires_in': app.config['JWT_ACCESS_TOKEN_EXPIRES'].total_seconds(),
            'user': new_user.to_dict(),
            'company': new_user.company.to_dict() if new_user.company else None
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Registration failed'}), 500

@app.route('/api/v1/auth/me', methods=['GET'])
@jwt_required()
def get_current_user():
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        return jsonify({
            'user': user.to_dict(),
            'company': user.company.to_dict() if user.company else None
        }), 200
        
    except Exception as e:
        return jsonify({'error': 'Failed to get user information'}), 500

@app.route('/api/v1/auth/logout', methods=['POST'])
@jwt_required()
def logout():
    return jsonify({'message': 'Logout successful'}), 200

@app.route('/api/v1/products', methods=['GET'])
def get_products():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        
        query = Product.query.filter_by(status='active')
        
        # Search filter
        search = request.args.get('search', '').strip()
        if search:
            query = query.filter(
                db.or_(
                    Product.name.ilike(f'%{search}%'),
                    Product.name_ar.ilike(f'%{search}%'),
                    Product.sku.ilike(f'%{search}%')
                )
            )
        
        # Supplier filter
        supplier_id = request.args.get('supplier_id', type=int)
        if supplier_id:
            query = query.filter(Product.supplier_id == supplier_id)
        
        # Paginate
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        products = pagination.items
        
        return jsonify({
            'products': [product.to_dict() for product in products],
            'pagination': {
                'current_page': page,
                'total_pages': pagination.pages,
                'total_items': pagination.total,
                'items_per_page': per_page,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': 'Failed to get products'}), 500

@app.route('/api/v1/products/my-products', methods=['GET'])
@jwt_required()
def get_my_products():
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user or user.user_type != 'supplier' or not user.company:
            return jsonify({'error': 'Only suppliers can access this endpoint'}), 403
        
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        
        query = Product.query.filter_by(supplier_id=user.company.id)
        
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        products = pagination.items
        
        return jsonify({
            'products': [product.to_dict() for product in products],
            'pagination': {
                'current_page': page,
                'total_pages': pagination.pages,
                'total_items': pagination.total,
                'items_per_page': per_page
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': 'Failed to get products'}), 500

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return jsonify({'error': 'Internal server error'}), 500

# JWT error handlers
@jwt.expired_token_loader
def expired_token_callback(jwt_header, jwt_payload):
    return jsonify({'error': 'Token has expired'}), 401

@jwt.invalid_token_loader
def invalid_token_callback(error):
    return jsonify({'error': 'Invalid token'}), 401

@jwt.unauthorized_loader
def missing_token_callback(error):
    return jsonify({'error': 'Authorization token is required'}), 401

def create_sample_data():
    """Create sample data for testing"""
    if User.query.first():
        return
    
    try:
        # Create sample supplier
        supplier_user = User(
            email='<EMAIL>',
            phone='+966501234567',
            user_type='supplier',
            first_name='أحمد',
            last_name='المورد'
        )
        supplier_user.set_password('Supplier123!')
        supplier_user.status = 'active'
        supplier_user.email_verified = True
        
        db.session.add(supplier_user)
        db.session.flush()
        
        supplier_company = Company(
            user_id=supplier_user.id,
            business_name='Modern Supplies Co.',
            business_name_ar='شركة الإمدادات الحديثة',
            business_type='supplier',
            commercial_registration='1234567890',
            industry_sector='food_beverage',
            verification_status='verified'
        )
        
        db.session.add(supplier_company)
        db.session.flush()
        
        # Create sample buyer
        buyer_user = User(
            email='<EMAIL>',
            phone='+966507654321',
            user_type='buyer',
            first_name='فاطمة',
            last_name='المشتري'
        )
        buyer_user.set_password('Buyer123!')
        buyer_user.status = 'active'
        buyer_user.email_verified = True
        
        db.session.add(buyer_user)
        db.session.flush()
        
        buyer_company = Company(
            user_id=buyer_user.id,
            business_name='Retail Market LLC',
            business_name_ar='شركة السوق للتجزئة',
            business_type='buyer',
            commercial_registration='0987654321',
            industry_sector='retail',
            verification_status='verified'
        )
        
        db.session.add(buyer_company)
        db.session.flush()
        
        # Create sample products
        products_data = [
            {
                'sku': 'RICE-001',
                'name': 'Premium Basmati Rice',
                'name_ar': 'أرز بسمتي فاخر',
                'description': 'High quality basmati rice from India',
                'unit_of_measure': 'kg',
                'unit_price': 25.50,
                'minimum_order_quantity': 100
            },
            {
                'sku': 'OIL-001',
                'name': 'Extra Virgin Olive Oil',
                'name_ar': 'زيت زيتون بكر ممتاز',
                'description': 'Premium extra virgin olive oil from Spain',
                'unit_of_measure': 'liter',
                'unit_price': 45.00,
                'minimum_order_quantity': 50
            }
        ]
        
        for product_data in products_data:
            product = Product(
                supplier_id=supplier_company.id,
                **product_data
            )
            db.session.add(product)
        
        db.session.commit()
        print("Sample data created successfully!")
        
    except Exception as e:
        db.session.rollback()
        print(f"Error creating sample data: {str(e)}")

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        create_sample_data()
    
    port = int(os.environ.get('PORT', 2340))
    app.run(host='0.0.0.0', port=port, debug=True)

