<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الموردين - منصة روافد B2B</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .rtl { direction: rtl; }
    </style>
</head>
<body class="bg-gray-50 rtl">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function SuppliersPage() {
            const [suppliers, setSuppliers] = useState([
                {
                    id: 1,
                    business_name: 'شركة الأغذية المتميزة',
                    business_name_ar: 'شركة الأغذية المتميزة',
                    industry_sector: 'food',
                    contact_email: '<EMAIL>',
                    contact_phone: '+966501234567',
                    address: 'الرياض، المملكة العربية السعودية',
                    rating: 4.8,
                    total_orders: 156,
                    products_count: 45
                },
                {
                    id: 2,
                    business_name: 'مؤسسة التوريدات الحديثة',
                    business_name_ar: 'مؤسسة التوريدات الحديثة',
                    industry_sector: 'retail',
                    contact_email: '<EMAIL>',
                    contact_phone: '+966507654321',
                    address: 'جدة، المملكة العربية السعودية',
                    rating: 4.6,
                    total_orders: 89,
                    products_count: 32
                },
                {
                    id: 3,
                    business_name: 'شركة المواد الغذائية الطازجة',
                    business_name_ar: 'شركة المواد الغذائية الطازجة',
                    industry_sector: 'food',
                    contact_email: '<EMAIL>',
                    contact_phone: '+966512345678',
                    address: 'الدمام، المملكة العربية السعودية',
                    rating: 4.9,
                    total_orders: 203,
                    products_count: 67
                }
            ]);
            const [loading, setLoading] = useState(false);

            const getSectorText = (sector) => {
                const sectorMap = {
                    'food': 'المواد الغذائية',
                    'retail': 'التجارة',
                    'manufacturing': 'التصنيع',
                    'technology': 'التكنولوجيا',
                    'healthcare': 'الرعاية الصحية'
                };
                return sectorMap[sector] || sector;
            };

            const renderStars = (rating) => {
                const stars = [];
                const fullStars = Math.floor(rating);
                const hasHalfStar = rating % 1 !== 0;

                for (let i = 0; i < fullStars; i++) {
                    stars.push(
                        <span key={i} className="text-yellow-400">★</span>
                    );
                }

                if (hasHalfStar) {
                    stars.push(
                        <span key="half" className="text-yellow-400">☆</span>
                    );
                }

                const emptyStars = 5 - Math.ceil(rating);
                for (let i = 0; i < emptyStars; i++) {
                    stars.push(
                        <span key={`empty-${i}`} className="text-gray-300">☆</span>
                    );
                }

                return stars;
            };

            return (
                <div className="min-h-screen bg-gray-50">
                    {/* Header */}
                    <header className="bg-white shadow">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="flex justify-between items-center py-6">
                                <div className="flex items-center">
                                    <h1 className="text-3xl font-bold text-gray-900">الموردين</h1>
                                </div>
                                <div className="flex items-center space-x-4">
                                    <a href="simple_index.html" className="text-indigo-600 hover:text-indigo-500">
                                        العودة للرئيسية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </header>

                    {/* Main Content */}
                    <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                        <div className="px-4 py-6 sm:px-0">
                            {loading ? (
                                <div className="text-center">
                                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto"></div>
                                    <p className="mt-4 text-lg text-gray-600">جاري تحميل الموردين...</p>
                                </div>
                            ) : (
                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                                    {suppliers.map((supplier) => (
                                        <div key={supplier.id} className="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow">
                                            <div className="px-4 py-5 sm:p-6">
                                                <div className="flex items-center justify-between mb-4">
                                                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                                                        {supplier.business_name_ar}
                                                    </h3>
                                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                        {getSectorText(supplier.industry_sector)}
                                                    </span>
                                                </div>

                                                {/* Rating */}
                                                <div className="flex items-center mb-3">
                                                    <div className="flex items-center">
                                                        {renderStars(supplier.rating)}
                                                    </div>
                                                    <span className="mr-2 text-sm text-gray-600">
                                                        ({supplier.rating})
                                                    </span>
                                                </div>

                                                {/* Contact Info */}
                                                <div className="space-y-2 mb-4">
                                                    <div className="flex items-center text-sm text-gray-600">
                                                        <span className="font-medium">البريد الإلكتروني:</span>
                                                        <span className="mr-2">{supplier.contact_email}</span>
                                                    </div>
                                                    <div className="flex items-center text-sm text-gray-600">
                                                        <span className="font-medium">الهاتف:</span>
                                                        <span className="mr-2">{supplier.contact_phone}</span>
                                                    </div>
                                                    <div className="flex items-center text-sm text-gray-600">
                                                        <span className="font-medium">العنوان:</span>
                                                        <span className="mr-2">{supplier.address}</span>
                                                    </div>
                                                </div>

                                                {/* Statistics */}
                                                <div className="border-t border-gray-200 pt-4">
                                                    <div className="grid grid-cols-2 gap-4 text-center">
                                                        <div>
                                                            <p className="text-2xl font-bold text-indigo-600">{supplier.total_orders}</p>
                                                            <p className="text-xs text-gray-500">إجمالي الطلبات</p>
                                                        </div>
                                                        <div>
                                                            <p className="text-2xl font-bold text-green-600">{supplier.products_count}</p>
                                                            <p className="text-xs text-gray-500">عدد المنتجات</p>
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Action Buttons */}
                                                <div className="mt-4 flex space-x-2">
                                                    <button className="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                                                        عرض المنتجات
                                                    </button>
                                                    <button className="flex-1 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                                                        تواصل معنا
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}

                            {suppliers.length === 0 && !loading && (
                                <div className="text-center py-12">
                                    <p className="text-gray-500 text-lg">لا توجد موردين متاحين حالياً</p>
                                </div>
                            )}
                        </div>
                    </main>
                </div>
            );
        }

        ReactDOM.render(<SuppliersPage />, document.getElementById('root'));
    </script>
</body>
</html>
