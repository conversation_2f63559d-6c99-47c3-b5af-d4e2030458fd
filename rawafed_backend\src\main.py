#!/usr/bin/env python3
"""
Rawafed B2B Platform - Main Application Entry Point
Enhanced version with PostgreSQL support and production-ready features
"""

import os
import sys
import logging
from datetime import datetime
from flask import Flask, request, jsonify, g
from flask_cors import CORS
from flask_jwt_extended import JWTManager
import psycopg2
from sqlalchemy import text

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from src.config import get_config, is_production, is_development
from src.models import init_db, db
from src.models.session import UserSession

def create_app(config_name=None):
    """Application factory pattern"""
    app = Flask(__name__)
    
    # Load configuration
    if config_name:
        app.config.from_object(config_name)
    else:
        config_class = get_config()
        app.config.from_object(config_class)
    
    # Initialize extensions
    init_extensions(app)
    
    # Register blueprints
    register_blueprints(app)
    
    # Setup logging
    setup_logging(app)
    
    # Setup error handlers
    setup_error_handlers(app)
    
    # Setup request hooks
    setup_request_hooks(app)
    
    return app

def init_extensions(app):
    """Initialize Flask extensions"""
    
    # Initialize database
    init_db(app)
    
    # Initialize CORS
    CORS(app, 
         origins=app.config.get('CORS_ORIGINS', ['*']),
         supports_credentials=True,
         allow_headers=['Content-Type', 'Authorization'],
         methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])
    
    # Initialize JWT
    jwt = JWTManager(app)
    
    # JWT configuration
    @jwt.token_in_blocklist_loader
    def check_if_token_revoked(jwt_header, jwt_payload):
        """Check if JWT token is revoked"""
        jti = jwt_payload['jti']
        # Check against database or cache
        # For now, return False (not revoked)
        return False
    
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        """Handle expired token"""
        return jsonify({
            'error': 'token_expired',
            'message': 'The token has expired'
        }), 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        """Handle invalid token"""
        return jsonify({
            'error': 'invalid_token',
            'message': 'Invalid token provided'
        }), 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        """Handle missing token"""
        return jsonify({
            'error': 'missing_token',
            'message': 'Authorization token is required'
        }), 401

def register_blueprints(app):
    """Register application blueprints"""
    
    # Import and register blueprints
    try:
        from src.routes.auth import auth_bp
        from src.routes.user import user_bp
        from src.routes.products import products_bp
        from src.routes.orders import orders_bp
        from src.routes.inventory import inventory_bp
from src.routes.admin import admin_bp
from src.routes.reports import reports_bp
        
        app.register_blueprint(auth_bp, url_prefix='/api/auth')
        app.register_blueprint(user_bp, url_prefix='/api/users')
        app.register_blueprint(products_bp, url_prefix='/api/products')
        app.register_blueprint(orders_bp, url_prefix='/api/orders')
        app.register_blueprint(inventory_bp, url_prefix='/api/inventory')
app.register_blueprint(admin_bp, url_prefix='/api/admin')
app.register_blueprint(reports_bp, url_prefix='/api/admin/reports')
        
    except ImportError as e:
        app.logger.warning(f"Could not import some blueprints: {e}")
    
    # Health check endpoint
    @app.route('/health')
    def health_check():
        """Health check endpoint"""
        try:
            # Test database connection
            db.session.execute(text('SELECT 1'))
            db_status = 'healthy'
        except Exception as e:
            db_status = f'unhealthy: {str(e)}'
        
        return jsonify({
            'status': 'healthy' if db_status == 'healthy' else 'unhealthy',
            'timestamp': datetime.utcnow().isoformat(),
            'database': db_status,
            'version': '2.0.0'
        })
    
    # API info endpoint
    @app.route('/api')
    def api_info():
        """API information endpoint"""
        return jsonify({
            'name': 'Rawafed B2B Platform API',
            'version': '2.0.0',
            'description': 'Enhanced B2B platform with PostgreSQL support',
            'endpoints': {
                'auth': '/api/auth',
                'users': '/api/users',
                'products': '/api/products',
                'orders': '/api/orders',
                'inventory': '/api/inventory'
            },
            'documentation': '/api/docs',
            'health': '/health'
        })

def setup_logging(app):
    """Setup application logging"""
    
    if not is_development():
        # Production logging
        logging.basicConfig(
            level=getattr(logging, app.config.get('LOG_LEVEL', 'INFO')),
            format='%(asctime)s %(levelname)s %(name)s %(message)s',
            handlers=[
                logging.FileHandler(app.config.get('LOG_FILE', 'app.log')),
                logging.StreamHandler(sys.stdout)
            ]
        )
    else:
        # Development logging
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s %(levelname)s %(name)s %(message)s'
        )
    
    # Suppress some verbose loggers in production
    if is_production():
        logging.getLogger('werkzeug').setLevel(logging.WARNING)
        logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)

def setup_error_handlers(app):
    """Setup error handlers"""
    
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({
            'error': 'bad_request',
            'message': 'Bad request'
        }), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        return jsonify({
            'error': 'unauthorized',
            'message': 'Unauthorized access'
        }), 401
    
    @app.errorhandler(403)
    def forbidden(error):
        return jsonify({
            'error': 'forbidden',
            'message': 'Access forbidden'
        }), 403
    
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'error': 'not_found',
            'message': 'Resource not found'
        }), 404
    
    @app.errorhandler(429)
    def rate_limit_exceeded(error):
        return jsonify({
            'error': 'rate_limit_exceeded',
            'message': 'Rate limit exceeded'
        }), 429
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        app.logger.error(f'Internal error: {error}')
        
        if is_development():
            return jsonify({
                'error': 'internal_error',
                'message': str(error)
            }), 500
        else:
            return jsonify({
                'error': 'internal_error',
                'message': 'Internal server error'
            }), 500

def setup_request_hooks(app):
    """Setup request hooks for logging and monitoring"""
    
    @app.before_request
    def before_request():
        """Log request information"""
        g.start_time = datetime.utcnow()
        
        # Log request in development
        if is_development():
            app.logger.debug(f'{request.method} {request.path} - {request.remote_addr}')
    
    @app.after_request
    def after_request(response):
        """Log response information and cleanup"""
        
        # Calculate request duration
        if hasattr(g, 'start_time'):
            duration = (datetime.utcnow() - g.start_time).total_seconds()
            
            # Log slow requests
            if duration > 1.0:  # Log requests taking more than 1 second
                app.logger.warning(f'Slow request: {request.method} {request.path} - {duration:.3f}s')
        
        # Add security headers
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        if is_production():
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        return response
    
    @app.teardown_appcontext
    def close_db(error):
        """Close database connection"""
        if error:
            db.session.rollback()
        db.session.remove()

def create_tables():
    """Create database tables"""
    with app.app_context():
        try:
            db.create_all()
            app.logger.info("Database tables created successfully")
        except Exception as e:
            app.logger.error(f"Error creating database tables: {e}")
            raise

def cleanup_expired_sessions():
    """Cleanup expired sessions"""
    try:
        expired_count = UserSession.cleanup_expired_sessions()
        if expired_count > 0:
            app.logger.info(f"Cleaned up {expired_count} expired sessions")
    except Exception as e:
        app.logger.error(f"Error cleaning up expired sessions: {e}")

# Create application instance
app = create_app()

if __name__ == '__main__':
    # Development server
    if is_development():
        app.logger.info("Starting development server...")
        app.run(
            host='0.0.0.0',
            port=int(os.environ.get('PORT', 5000)),
            debug=True,
            threaded=True
        )
    else:
        # Production server (use gunicorn or similar)
        app.logger.info("Application ready for production deployment")
        
        # Create tables if they don't exist
        create_tables()
        
        # Cleanup expired sessions on startup
        cleanup_expired_sessions()

