<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة روافد B2B - الصفحة الرئيسية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .rtl { direction: rtl; }
    </style>
</head>
<body class="bg-gray-50 rtl">
    <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <!-- Header -->
        <header class="bg-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-center items-center py-6">
                    <h1 class="text-4xl font-bold text-gray-900">منصة روافد B2B</h1>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-extrabold text-gray-900 mb-4">
                    مرحباً بك في منصة روافد للتجارة الإلكترونية بين الشركات
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    منصة متكاملة تربط بين الموردين والمشترين في المملكة العربية السعودية
                </p>
            </div>

            <!-- Features Grid -->
            <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3 mb-12">
                <!-- Login/Dashboard -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg hover:shadow-xl transition-shadow">
                    <div class="px-6 py-8">
                        <div class="flex items-center justify-center w-12 h-12 bg-indigo-100 rounded-md mb-4 mx-auto">
                            <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 text-center mb-2">لوحة التحكم</h3>
                        <p class="text-gray-600 text-center mb-4">تسجيل الدخول وإدارة حسابك</p>
                        <div class="text-center">
                            <a href="simple_index.html" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                دخول النظام
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Products -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg hover:shadow-xl transition-shadow">
                    <div class="px-6 py-8">
                        <div class="flex items-center justify-center w-12 h-12 bg-green-100 rounded-md mb-4 mx-auto">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 text-center mb-2">المنتجات</h3>
                        <p class="text-gray-600 text-center mb-4">تصفح وإدارة المنتجات المتاحة</p>
                        <div class="text-center">
                            <a href="products.html" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                                عرض المنتجات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Orders -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg hover:shadow-xl transition-shadow">
                    <div class="px-6 py-8">
                        <div class="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-md mb-4 mx-auto">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 text-center mb-2">الطلبات</h3>
                        <p class="text-gray-600 text-center mb-4">إدارة ومتابعة الطلبات</p>
                        <div class="text-center">
                            <a href="orders.html" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                عرض الطلبات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Suppliers -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg hover:shadow-xl transition-shadow">
                    <div class="px-6 py-8">
                        <div class="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-md mb-4 mx-auto">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 text-center mb-2">الموردين</h3>
                        <p class="text-gray-600 text-center mb-4">تصفح قائمة الموردين المعتمدين</p>
                        <div class="text-center">
                            <a href="suppliers.html" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                                عرض الموردين
                            </a>
                        </div>
                    </div>
                </div>

                <!-- API Status -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg hover:shadow-xl transition-shadow">
                    <div class="px-6 py-8">
                        <div class="flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-md mb-4 mx-auto">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 text-center mb-2">حالة النظام</h3>
                        <p class="text-gray-600 text-center mb-4">التحقق من حالة الخادم</p>
                        <div class="text-center">
                            <button onclick="checkAPIStatus()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700">
                                فحص الحالة
                            </button>
                        </div>
                        <div id="api-status" class="mt-4 text-center"></div>
                    </div>
                </div>

                <!-- Documentation -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg hover:shadow-xl transition-shadow">
                    <div class="px-6 py-8">
                        <div class="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-md mb-4 mx-auto">
                            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 text-center mb-2">معلومات النظام</h3>
                        <p class="text-gray-600 text-center mb-4">تفاصيل تقنية حول المنصة</p>
                        <div class="text-center">
                            <button onclick="showSystemInfo()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700">
                                عرض المعلومات
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="bg-white shadow-lg rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">حالة الخوادم</h3>
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div class="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                        <div>
                            <p class="text-sm font-medium text-green-800">Backend API</p>
                            <p class="text-xs text-green-600">http://localhost:2340</p>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
                            <span class="text-sm text-green-800">متصل</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                        <div>
                            <p class="text-sm font-medium text-blue-800">Frontend Server</p>
                            <p class="text-xs text-blue-600">http://localhost:3000</p>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-blue-400 rounded-full mr-2"></div>
                            <span class="text-sm text-blue-800">متصل</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t border-gray-200">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <p class="text-center text-gray-500 text-sm">
                    © 2025 منصة روافد B2B. جميع الحقوق محفوظة.
                </p>
            </div>
        </footer>
    </div>

    <script>
        async function checkAPIStatus() {
            const statusDiv = document.getElementById('api-status');
            statusDiv.innerHTML = '<div class="text-sm text-gray-600">جاري الفحص...</div>';
            
            try {
                const response = await fetch('http://localhost:2340/api/health');
                const data = await response.json();
                
                if (response.ok) {
                    statusDiv.innerHTML = `
                        <div class="text-sm text-green-600">
                            ✅ الخادم يعمل بشكل طبيعي<br>
                            <span class="text-xs">${data.message}</span>
                        </div>
                    `;
                } else {
                    throw new Error('Server error');
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="text-sm text-red-600">
                        ❌ خطأ في الاتصال بالخادم<br>
                        <span class="text-xs">تأكد من تشغيل الخادم على المنفذ 2340</span>
                    </div>
                `;
            }
        }

        function showSystemInfo() {
            alert(`معلومات النظام:
            
Frontend: React + Tailwind CSS
Backend: Flask + SQLAlchemy
Database: SQLite
Port: 2340 (Backend), 3000 (Frontend)

الميزات المتاحة:
- تسجيل الدخول والتسجيل
- إدارة المنتجات
- إدارة الطلبات
- عرض الموردين
- واجهة مستخدم متجاوبة`);
        }

        // Auto-check API status on page load
        window.addEventListener('load', checkAPIStatus);
    </script>
</body>
</html>
