<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الطلبات - منصة روافد B2B</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .rtl { direction: rtl; }
    </style>
</head>
<body class="bg-gray-50 rtl">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        const API_BASE_URL = 'http://localhost:2340/api';

        const apiService = {
            async request(endpoint, options = {}) {
                const url = `${API_BASE_URL}${endpoint}`;
                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options,
                };

                try {
                    const response = await fetch(url, config);
                    const data = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(data.error || 'API request failed');
                    }
                    
                    return data;
                } catch (error) {
                    console.error(`API Error (${endpoint}):`, error);
                    throw error;
                }
            },

            async getOrders() {
                return await this.request('/v1/orders');
            },

            async createOrder(orderData) {
                return await this.request('/v1/orders', {
                    method: 'POST',
                    body: JSON.stringify(orderData),
                });
            }
        };

        function OrdersPage() {
            const [orders, setOrders] = useState([]);
            const [loading, setLoading] = useState(true);
            const [showCreateForm, setShowCreateForm] = useState(false);
            const [newOrder, setNewOrder] = useState({
                supplier_id: 1,
                items: [
                    {
                        product_id: 1,
                        quantity: 1,
                        unit_price: 0
                    }
                ]
            });

            useEffect(() => {
                loadOrders();
            }, []);

            const loadOrders = async () => {
                try {
                    const response = await apiService.getOrders();
                    setOrders(response.orders || []);
                } catch (error) {
                    console.error('Error loading orders:', error);
                    // Create some sample orders for demo
                    setOrders([
                        {
                            id: 1,
                            order_number: 'ORD-001',
                            status: 'pending',
                            total_amount: 150.00,
                            created_at: '2025-01-14T10:30:00Z',
                            supplier: { business_name: 'شركة الأغذية المتميزة' },
                            items: [
                                { product: { name: 'أرز بسمتي', name_ar: 'أرز بسمتي' }, quantity: 10, unit_price: 15.00 }
                            ]
                        },
                        {
                            id: 2,
                            order_number: 'ORD-002',
                            status: 'confirmed',
                            total_amount: 300.00,
                            created_at: '2025-01-13T14:20:00Z',
                            supplier: { business_name: 'مؤسسة التوريدات الحديثة' },
                            items: [
                                { product: { name: 'زيت زيتون', name_ar: 'زيت زيتون' }, quantity: 5, unit_price: 60.00 }
                            ]
                        }
                    ]);
                } finally {
                    setLoading(false);
                }
            };

            const handleCreateOrder = async (e) => {
                e.preventDefault();
                try {
                    await apiService.createOrder(newOrder);
                    setShowCreateForm(false);
                    setNewOrder({
                        supplier_id: 1,
                        items: [
                            {
                                product_id: 1,
                                quantity: 1,
                                unit_price: 0
                            }
                        ]
                    });
                    loadOrders();
                } catch (error) {
                    alert('خطأ في إنشاء الطلب: ' + error.message);
                }
            };

            const getStatusText = (status) => {
                const statusMap = {
                    'pending': 'في الانتظار',
                    'confirmed': 'مؤكد',
                    'shipped': 'تم الشحن',
                    'delivered': 'تم التسليم',
                    'cancelled': 'ملغي'
                };
                return statusMap[status] || status;
            };

            const getStatusColor = (status) => {
                const colorMap = {
                    'pending': 'bg-yellow-100 text-yellow-800',
                    'confirmed': 'bg-blue-100 text-blue-800',
                    'shipped': 'bg-purple-100 text-purple-800',
                    'delivered': 'bg-green-100 text-green-800',
                    'cancelled': 'bg-red-100 text-red-800'
                };
                return colorMap[status] || 'bg-gray-100 text-gray-800';
            };

            return (
                <div className="min-h-screen bg-gray-50">
                    {/* Header */}
                    <header className="bg-white shadow">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="flex justify-between items-center py-6">
                                <div className="flex items-center">
                                    <h1 className="text-3xl font-bold text-gray-900">الطلبات</h1>
                                </div>
                                <div className="flex items-center space-x-4">
                                    <button
                                        onClick={() => setShowCreateForm(true)}
                                        className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                                    >
                                        طلب جديد
                                    </button>
                                    <a href="simple_index.html" className="text-indigo-600 hover:text-indigo-500">
                                        العودة للرئيسية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </header>

                    {/* Main Content */}
                    <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                        <div className="px-4 py-6 sm:px-0">
                            {loading ? (
                                <div className="text-center">
                                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto"></div>
                                    <p className="mt-4 text-lg text-gray-600">جاري تحميل الطلبات...</p>
                                </div>
                            ) : (
                                <div className="space-y-6">
                                    {orders.map((order) => (
                                        <div key={order.id} className="bg-white overflow-hidden shadow rounded-lg">
                                            <div className="px-4 py-5 sm:p-6">
                                                <div className="flex justify-between items-start mb-4">
                                                    <div>
                                                        <h3 className="text-lg leading-6 font-medium text-gray-900">
                                                            طلب رقم: {order.order_number}
                                                        </h3>
                                                        <p className="text-sm text-gray-500">
                                                            المورد: {order.supplier?.business_name}
                                                        </p>
                                                        <p className="text-sm text-gray-500">
                                                            تاريخ الطلب: {new Date(order.created_at).toLocaleDateString('ar-SA')}
                                                        </p>
                                                    </div>
                                                    <div className="text-left">
                                                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                                                            {getStatusText(order.status)}
                                                        </span>
                                                        <p className="text-lg font-bold text-green-600 mt-2">
                                                            {order.total_amount} ريال
                                                        </p>
                                                    </div>
                                                </div>

                                                {/* Order Items */}
                                                <div className="border-t border-gray-200 pt-4">
                                                    <h4 className="text-sm font-medium text-gray-900 mb-2">عناصر الطلب:</h4>
                                                    <div className="space-y-2">
                                                        {order.items?.map((item, index) => (
                                                            <div key={index} className="flex justify-between items-center text-sm">
                                                                <span className="text-gray-900">
                                                                    {item.product?.name_ar || item.product?.name}
                                                                </span>
                                                                <span className="text-gray-500">
                                                                    الكمية: {item.quantity} × {item.unit_price} ريال
                                                                </span>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}

                            {orders.length === 0 && !loading && (
                                <div className="text-center py-12">
                                    <p className="text-gray-500 text-lg">لا توجد طلبات حالياً</p>
                                </div>
                            )}
                        </div>
                    </main>

                    {/* Create Order Modal */}
                    {showCreateForm && (
                        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                                <div className="mt-3">
                                    <h3 className="text-lg font-medium text-gray-900 mb-4">طلب جديد</h3>
                                    <form onSubmit={handleCreateOrder} className="space-y-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                المورد
                                            </label>
                                            <select
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                value={newOrder.supplier_id}
                                                onChange={(e) => setNewOrder({...newOrder, supplier_id: parseInt(e.target.value)})}
                                            >
                                                <option value={1}>شركة الأغذية المتميزة</option>
                                                <option value={2}>مؤسسة التوريدات الحديثة</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                المنتج
                                            </label>
                                            <select
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                value={newOrder.items[0].product_id}
                                                onChange={(e) => setNewOrder({
                                                    ...newOrder,
                                                    items: [{
                                                        ...newOrder.items[0],
                                                        product_id: parseInt(e.target.value)
                                                    }]
                                                })}
                                            >
                                                <option value={1}>أرز بسمتي</option>
                                                <option value={2}>زيت زيتون</option>
                                                <option value={3}>سكر أبيض</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                الكمية
                                            </label>
                                            <input
                                                type="number"
                                                min="1"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                value={newOrder.items[0].quantity}
                                                onChange={(e) => setNewOrder({
                                                    ...newOrder,
                                                    items: [{
                                                        ...newOrder.items[0],
                                                        quantity: parseInt(e.target.value)
                                                    }]
                                                })}
                                                required
                                            />
                                        </div>

                                        <div className="flex justify-end space-x-2">
                                            <button
                                                type="button"
                                                onClick={() => setShowCreateForm(false)}
                                                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                                            >
                                                إلغاء
                                            </button>
                                            <button
                                                type="submit"
                                                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                                            >
                                                إنشاء طلب
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            );
        }

        ReactDOM.render(<OrdersPage />, document.getElementById('root'));
    </script>
</body>
</html>
