<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المنتجات - منصة روافد B2B</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .rtl { direction: rtl; }
    </style>
</head>
<body class="bg-gray-50 rtl">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        const API_BASE_URL = 'http://localhost:2340/api/v1';

        const apiService = {
            async request(endpoint, options = {}) {
                const url = `${API_BASE_URL}${endpoint}`;
                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options,
                };

                try {
                    const response = await fetch(url, config);
                    const data = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(data.error || 'API request failed');
                    }
                    
                    return data;
                } catch (error) {
                    console.error(`API Error (${endpoint}):`, error);
                    throw error;
                }
            },

            async getProducts() {
                return await this.request('/products');
            },

            async createProduct(productData) {
                return await this.request('/products', {
                    method: 'POST',
                    body: JSON.stringify(productData),
                });
            }
        };

        function ProductsPage() {
            const [products, setProducts] = useState([]);
            const [loading, setLoading] = useState(true);
            const [showAddForm, setShowAddForm] = useState(false);
            const [newProduct, setNewProduct] = useState({
                name: '',
                name_ar: '',
                description: '',
                description_ar: '',
                unit_price: '',
                unit_of_measure: 'kg',
                minimum_order_quantity: '',
                category: 'food',
                supplier_id: 1
            });

            useEffect(() => {
                loadProducts();
            }, []);

            const loadProducts = async () => {
                try {
                    const response = await apiService.getProducts();
                    setProducts(response.products || []);
                } catch (error) {
                    console.error('Error loading products:', error);
                } finally {
                    setLoading(false);
                }
            };

            const handleAddProduct = async (e) => {
                e.preventDefault();
                try {
                    await apiService.createProduct({
                        ...newProduct,
                        unit_price: parseFloat(newProduct.unit_price),
                        minimum_order_quantity: parseInt(newProduct.minimum_order_quantity)
                    });
                    setShowAddForm(false);
                    setNewProduct({
                        name: '',
                        name_ar: '',
                        description: '',
                        description_ar: '',
                        unit_price: '',
                        unit_of_measure: 'kg',
                        minimum_order_quantity: '',
                        category: 'food',
                        supplier_id: 1
                    });
                    loadProducts();
                } catch (error) {
                    alert('خطأ في إضافة المنتج: ' + error.message);
                }
            };

            return (
                <div className="min-h-screen bg-gray-50">
                    {/* Header */}
                    <header className="bg-white shadow">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="flex justify-between items-center py-6">
                                <div className="flex items-center">
                                    <h1 className="text-3xl font-bold text-gray-900">المنتجات</h1>
                                </div>
                                <div className="flex items-center space-x-4">
                                    <button
                                        onClick={() => setShowAddForm(true)}
                                        className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                                    >
                                        إضافة منتج جديد
                                    </button>
                                    <a href="simple_index.html" className="text-indigo-600 hover:text-indigo-500">
                                        العودة للرئيسية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </header>

                    {/* Main Content */}
                    <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                        <div className="px-4 py-6 sm:px-0">
                            {loading ? (
                                <div className="text-center">
                                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto"></div>
                                    <p className="mt-4 text-lg text-gray-600">جاري تحميل المنتجات...</p>
                                </div>
                            ) : (
                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                                    {products.map((product) => (
                                        <div key={product.id} className="bg-white overflow-hidden shadow rounded-lg">
                                            <div className="px-4 py-5 sm:p-6">
                                                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-2">
                                                    {product.name}
                                                </h3>
                                                <p className="text-sm text-gray-500 mb-2">{product.name_ar}</p>
                                                <p className="text-sm text-gray-600 mb-4">{product.description}</p>
                                                <div className="flex justify-between items-center mb-2">
                                                    <span className="text-lg font-bold text-green-600">
                                                        {product.unit_price} ريال
                                                    </span>
                                                    <span className="text-sm text-gray-500">/{product.unit_of_measure}</span>
                                                </div>
                                                <p className="text-xs text-gray-400">الحد الأدنى: {product.minimum_order_quantity}</p>
                                                <div className="mt-4">
                                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                        {product.category}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}

                            {products.length === 0 && !loading && (
                                <div className="text-center py-12">
                                    <p className="text-gray-500 text-lg">لا توجد منتجات متاحة حالياً</p>
                                </div>
                            )}
                        </div>
                    </main>

                    {/* Add Product Modal */}
                    {showAddForm && (
                        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                                <div className="mt-3">
                                    <h3 className="text-lg font-medium text-gray-900 mb-4">إضافة منتج جديد</h3>
                                    <form onSubmit={handleAddProduct} className="space-y-4">
                                        <input
                                            type="text"
                                            placeholder="اسم المنتج (بالإنجليزية)"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                            value={newProduct.name}
                                            onChange={(e) => setNewProduct({...newProduct, name: e.target.value})}
                                            required
                                        />
                                        <input
                                            type="text"
                                            placeholder="اسم المنتج (بالعربية)"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                            value={newProduct.name_ar}
                                            onChange={(e) => setNewProduct({...newProduct, name_ar: e.target.value})}
                                        />
                                        <textarea
                                            placeholder="وصف المنتج"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                            rows="3"
                                            value={newProduct.description}
                                            onChange={(e) => setNewProduct({...newProduct, description: e.target.value})}
                                        />
                                        <input
                                            type="number"
                                            step="0.01"
                                            placeholder="السعر"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                            value={newProduct.unit_price}
                                            onChange={(e) => setNewProduct({...newProduct, unit_price: e.target.value})}
                                            required
                                        />
                                        <select
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                            value={newProduct.unit_of_measure}
                                            onChange={(e) => setNewProduct({...newProduct, unit_of_measure: e.target.value})}
                                        >
                                            <option value="kg">كيلوجرام</option>
                                            <option value="piece">قطعة</option>
                                            <option value="liter">لتر</option>
                                            <option value="box">صندوق</option>
                                        </select>
                                        <input
                                            type="number"
                                            placeholder="الحد الأدنى للطلب"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                            value={newProduct.minimum_order_quantity}
                                            onChange={(e) => setNewProduct({...newProduct, minimum_order_quantity: e.target.value})}
                                            required
                                        />
                                        <div className="flex justify-end space-x-2">
                                            <button
                                                type="button"
                                                onClick={() => setShowAddForm(false)}
                                                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                                            >
                                                إلغاء
                                            </button>
                                            <button
                                                type="submit"
                                                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                                            >
                                                إضافة
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            );
        }

        ReactDOM.render(<ProductsPage />, document.getElementById('root'));
    </script>
</body>
</html>
