/**
 * API Service for Rawafed B2B Platform
 * Handles all API communications with the backend
 */

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:2340/api/v1'

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL
    this.token = localStorage.getItem('authToken')
  }

  // Helper method to get headers
  getHeaders(includeAuth = true) {
    const headers = {
      'Content-Type': 'application/json',
    }

    if (includeAuth && this.token) {
      headers['Authorization'] = `Bearer ${this.token}`
    }

    return headers
  }

  // Helper method to handle responses
  async handleResponse(response) {
    const data = await response.json()
    
    if (!response.ok) {
      throw new Error(data.error || 'API request failed')
    }
    
    return data
  }

  // Helper method to make requests
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`
    const config = {
      headers: this.getHeaders(options.auth !== false),
      ...options,
    }

    try {
      const response = await fetch(url, config)
      return await this.handleResponse(response)
    } catch (error) {
      console.error(`API Error (${endpoint}):`, error)
      throw error
    }
  }

  // Update token
  setToken(token) {
    this.token = token
    if (token) {
      localStorage.setItem('authToken', token)
    } else {
      localStorage.removeItem('authToken')
    }
  }

  // Authentication APIs
  async login(email, password) {
    const response = await this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
      auth: false,
    })
    
    if (response.access_token) {
      this.setToken(response.access_token)
    }
    
    return response
  }

  async register(userData) {
    const response = await this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
      auth: false,
    })
    
    if (response.access_token) {
      this.setToken(response.access_token)
    }
    
    return response
  }

  async logout() {
    try {
      await this.request('/auth/logout', {
        method: 'POST',
      })
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      this.setToken(null)
    }
  }

  async getCurrentUser() {
    return await this.request('/auth/me')
  }

  async changePassword(currentPassword, newPassword) {
    return await this.request('/auth/change-password', {
      method: 'POST',
      body: JSON.stringify({
        current_password: currentPassword,
        new_password: newPassword,
      }),
    })
  }

  async refreshToken() {
    const refreshToken = localStorage.getItem('refreshToken')
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await this.request('/auth/refresh', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${refreshToken}`,
        'Content-Type': 'application/json',
      },
    })

    if (response.access_token) {
      this.setToken(response.access_token)
    }

    return response
  }

  // Products APIs
  async getProducts(params = {}) {
    const queryString = new URLSearchParams(params).toString()
    return await this.request(`/products?${queryString}`)
  }

  async getProduct(productId) {
    return await this.request(`/products/${productId}`)
  }

  async createProduct(productData) {
    return await this.request('/products', {
      method: 'POST',
      body: JSON.stringify(productData),
    })
  }

  async updateProduct(productId, productData) {
    return await this.request(`/products/${productId}`, {
      method: 'PUT',
      body: JSON.stringify(productData),
    })
  }

  async deleteProduct(productId) {
    return await this.request(`/products/${productId}`, {
      method: 'DELETE',
    })
  }

  async getMyProducts(params = {}) {
    const queryString = new URLSearchParams(params).toString()
    return await this.request(`/products/my-products?${queryString}`)
  }

  async searchProducts(query, filters = {}) {
    const params = { q: query, ...filters }
    const queryString = new URLSearchParams(params).toString()
    return await this.request(`/products/search?${queryString}`)
  }

  async getProductCategories() {
    return await this.request('/products/categories')
  }

  async getProductPricing(productId) {
    return await this.request(`/products/${productId}/pricing`)
  }

  // Orders APIs
  async getOrders(params = {}) {
    const queryString = new URLSearchParams(params).toString()
    return await this.request(`/orders?${queryString}`)
  }

  async getOrder(orderId) {
    return await this.request(`/orders/${orderId}`)
  }

  async createOrder(orderData) {
    return await this.request('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    })
  }

  async updateOrder(orderId, orderData) {
    return await this.request(`/orders/${orderId}`, {
      method: 'PUT',
      body: JSON.stringify(orderData),
    })
  }

  async updateOrderStatus(orderId, status, notes = '') {
    return await this.request(`/orders/${orderId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status, notes }),
    })
  }

  async cancelOrder(orderId) {
    return await this.request(`/orders/${orderId}`, {
      method: 'DELETE',
    })
  }

  async getOrderStatistics() {
    return await this.request('/orders/statistics')
  }

  async getOverdueOrders() {
    return await this.request('/orders/overdue')
  }

  // Inventory APIs
  async getInventory(params = {}) {
    const queryString = new URLSearchParams(params).toString()
    return await this.request(`/inventory?${queryString}`)
  }

  async getInventoryItem(inventoryId) {
    return await this.request(`/inventory/${inventoryId}`)
  }

  async createInventory(inventoryData) {
    return await this.request('/inventory', {
      method: 'POST',
      body: JSON.stringify(inventoryData),
    })
  }

  async updateInventory(inventoryId, inventoryData) {
    return await this.request(`/inventory/${inventoryId}`, {
      method: 'PUT',
      body: JSON.stringify(inventoryData),
    })
  }

  async adjustInventory(inventoryId, adjustment, reason) {
    return await this.request(`/inventory/${inventoryId}/adjust`, {
      method: 'POST',
      body: JSON.stringify({ adjustment, reason }),
    })
  }

  async markInventoryDamaged(inventoryId, quantity, reason) {
    return await this.request(`/inventory/${inventoryId}/damage`, {
      method: 'POST',
      body: JSON.stringify({ quantity, reason }),
    })
  }

  async getLowStockItems(warehouseId = null) {
    const params = warehouseId ? { warehouse_id: warehouseId } : {}
    const queryString = new URLSearchParams(params).toString()
    return await this.request(`/inventory/low-stock?${queryString}`)
  }

  async getExpiredItems(warehouseId = null) {
    const params = warehouseId ? { warehouse_id: warehouseId } : {}
    const queryString = new URLSearchParams(params).toString()
    return await this.request(`/inventory/expired?${queryString}`)
  }

  async getNearExpiryItems(days = 30, warehouseId = null) {
    const params = { days }
    if (warehouseId) params.warehouse_id = warehouseId
    const queryString = new URLSearchParams(params).toString()
    return await this.request(`/inventory/near-expiry?${queryString}`)
  }

  async getInventorySummary(warehouseId = null) {
    const params = warehouseId ? { warehouse_id: warehouseId } : {}
    const queryString = new URLSearchParams(params).toString()
    return await this.request(`/inventory/summary?${queryString}`)
  }

  // Health check
  async healthCheck() {
    return await this.request('/health', { auth: false })
  }
}

// Create and export a singleton instance
const apiService = new ApiService()

export default apiService

// Export individual methods for convenience
export const {
  login,
  register,
  logout,
  getCurrentUser,
  changePassword,
  refreshToken,
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getMyProducts,
  searchProducts,
  getProductCategories,
  getProductPricing,
  getOrders,
  getOrder,
  createOrder,
  updateOrder,
  updateOrderStatus,
  cancelOrder,
  getOrderStatistics,
  getOverdueOrders,
  getInventory,
  getInventoryItem,
  createInventory,
  updateInventory,
  adjustInventory,
  markInventoryDamaged,
  getLowStockItems,
  getExpiredItems,
  getNearExpiryItems,
  getInventorySummary,
  healthCheck,
} = apiService

